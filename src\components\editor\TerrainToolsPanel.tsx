import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import {
  setTerrainBrushSize,
  setTerrainBrushStrength,
  setTerrainTool,
  setTerrainColor,
  toggleTerrainWireframe
} from '../../store/editor';
import { EditorMode } from '../../types/editorTypes';
import './TerrainToolsPanel.scss';

interface TerrainToolsPanelProps {
  className?: string;
}

export const TerrainToolsPanel: React.FC<TerrainToolsPanelProps> = ({ className }) => {
  const dispatch = useDispatch();
  const editorState = useSelector((state: RootState) => state.editor.editorState);

  if (editorState.mode !== EditorMode.TERRAIN_SCULPTING) {
    return null;
  }

  const handleBrushSizeChange = (value: number) => {
    dispatch(setTerrainBrushSize(value));
  };

  const handleBrushStrengthChange = (value: number) => {
    dispatch(setTerrainBrushStrength(value));
  };

  const handleToolChange = (tool: 'raise' | 'lower' | 'smooth' | 'flatten') => {
    dispatch(setTerrainTool(tool));
  };

  const handleColorChange = (color: string) => {
    dispatch(setTerrainColor(color));
  };

  const handleWireframeToggle = () => {
    dispatch(toggleTerrainWireframe());
  };

  return (
    <div className={`terrain-tools-panel ${className || ''}`}>
      <div className="panel-header">
        <h3>Terrain Sculpting</h3>
        <p>Modify the terrain height and shape to create natural landscapes.</p>
      </div>

      {/* Terrain Tools */}
      <div className="tool-section">
        <h4>Terrain Tools</h4>
        <div className="tool-buttons">
          <button
            className={`tool-button ${editorState.terrainTool === 'raise' ? 'active' : ''}`}
            onClick={() => handleToolChange('raise')}
            title="Raise terrain height"
          >
            ⛰️ Raise
          </button>
          <button
            className={`tool-button ${editorState.terrainTool === 'lower' ? 'active' : ''}`}
            onClick={() => handleToolChange('lower')}
            title="Lower terrain height"
          >
            🕳️ Lower
          </button>
          <button
            className={`tool-button ${editorState.terrainTool === 'smooth' ? 'active' : ''}`}
            onClick={() => handleToolChange('smooth')}
            title="Smooth terrain surface"
          >
            🌊 Smooth
          </button>
          <button
            className={`tool-button ${editorState.terrainTool === 'flatten' ? 'active' : ''}`}
            onClick={() => handleToolChange('flatten')}
            title="Flatten terrain to base level"
          >
            📏 Flatten
          </button>
        </div>
      </div>

      {/* Brush Settings */}
      <div className="brush-section">
        <h4>Brush Settings</h4>

        <div className="setting-group">
          <label htmlFor="brush-size">Brush Size</label>
          <div className="slider-container">
            <input
              id="brush-size"
              type="range"
              min="1"
              max="20"
              step="1"
              value={editorState.terrainBrushSize}
              onChange={(e) => handleBrushSizeChange(Number(e.target.value))}
              className="slider"
            />
            <span className="value">{editorState.terrainBrushSize}</span>
          </div>
        </div>

        <div className="setting-group">
          <label htmlFor="brush-strength">Brush Strength</label>
          <div className="slider-container">
            <input
              id="brush-strength"
              type="range"
              min="0.01"
              max="1.0"
              step="0.01"
              value={editorState.terrainBrushStrength}
              onChange={(e) => handleBrushStrengthChange(Number(e.target.value))}
              className="slider"
            />
            <span className="value">{editorState.terrainBrushStrength.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Terrain Presets */}
      <div className="presets-section">
        <h4>Terrain Presets</h4>
        <div className="preset-buttons">
          <button
            className="preset-button"
            onClick={() => {
              // Apply flat desert preset
              handleColorChange('#D2B48C');
              handleToolChange('flatten');
            }}
            title="Flat Desert"
          >
            🏜️ Flat Desert
          </button>
          <button
            className="preset-button"
            onClick={() => {
              // Apply rolling hills preset
              handleColorChange('#8B7355');
              handleToolChange('smooth');
            }}
            title="Rolling Hills"
          >
            🏔️ Rolling Hills
          </button>
          <button
            className="preset-button"
            onClick={() => {
              // Apply crater preset
              handleColorChange('#A0522D');
              handleToolChange('lower');
            }}
            title="Crater"
          >
            🌋 Crater
          </button>
          <button
            className="preset-button"
            onClick={() => {
              // Apply valley preset
              handleColorChange('#CD853F');
              handleToolChange('raise');
            }}
            title="Valley"
          >
            🏞️ Valley
          </button>
        </div>
      </div>

      {/* Material Settings */}
      <div className="material-section">
        <h4>Terrain Material</h4>

        <div className="setting-group">
          <label htmlFor="terrain-color">Color</label>
          <div className="color-container">
            <input
              id="terrain-color"
              type="color"
              value={editorState.terrainColor}
              onChange={(e) => handleColorChange(e.target.value)}
              className="color-picker"
            />
            <span className="color-value">{editorState.terrainColor}</span>
          </div>
        </div>

        <div className="setting-group">
          <label>
            <input
              type="checkbox"
              checked={editorState.terrainWireframe}
              onChange={handleWireframeToggle}
            />
            Wireframe Mode
          </label>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="actions-section">
        <h4>Quick Actions</h4>
        <div className="action-buttons">
          <button
            className="action-button"
            onClick={() => {
              // Reset terrain to flat
              handleToolChange('flatten');
              handleBrushSizeChange(10);
              handleBrushStrengthChange(1.0);
            }}
            title="Reset terrain to flat surface"
          >
            🔄 Reset Terrain
          </button>
          <button
            className="action-button"
            onClick={() => {
              // Generate random terrain
              handleToolChange('raise');
              handleBrushSizeChange(8);
              handleBrushStrengthChange(0.3);
            }}
            title="Generate random terrain features"
          >
            🎲 Random Terrain
          </button>
        </div>
      </div>

      {/* Instructions */}
      <div className="instructions-section">
        <h4>Instructions</h4>
        <ul className="instruction-list">
          <li>Click and drag on the terrain to sculpt</li>
          <li>Use different tools for various effects</li>
          <li>Adjust brush size for broader or finer control</li>
          <li>Modify strength for subtle or dramatic changes</li>
          <li>Use presets for quick terrain styles</li>
        </ul>
      </div>
    </div>
  );
};
