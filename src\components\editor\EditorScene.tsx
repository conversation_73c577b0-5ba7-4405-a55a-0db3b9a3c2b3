import React, { useRef, useState, useCallback, useEffect } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { OrbitControls, TransformControls } from '@react-three/drei';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import {
  addTile,
  addObject,
  selectObjects,
  setCameraPosition,
  updateObject,
  removeObject
} from '../../store/editor';
import { EditorMode, EditorTool, PlacedObject } from '../../types/editorTypes';
import { TileType } from '../../types/gameTypes';
import { EditorTile } from './EditorTile';
import { EditorObject } from './EditorObject';
import { EditorGrid } from './EditorGrid';
import { TerrainSystem } from './TerrainSystem';
import * as THREE from 'three';

export const EditorScene: React.FC = () => {
  const dispatch = useDispatch();
  const { camera, raycaster, scene, gl } = useThree();
  const {
    currentMap,
    editorState,
    selectedObjects,
    previewMode
  } = useSelector((state: RootState) => state.editor);

  const [hoveredObject, setHoveredObject] = useState<string | null>(null);
  const [selectedTiles, setSelectedTiles] = useState<number[]>([]);
  const [hoveredTile, setHoveredTile] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [mousePosition, setMousePosition] = useState(new THREE.Vector2());
  const transformControlsRef = useRef<any>(null);
  const orbitControlsRef = useRef<any>(null);
  const groundPlaneRef = useRef<THREE.Mesh>(null);
  const terrainRef = useRef<THREE.Mesh>(null);

  // Update camera position in store when it changes
  useFrame(() => {
    if (camera && !isDragging) {
      const position = camera.position.toArray() as [number, number, number];
      const target = new THREE.Vector3();
      camera.getWorldDirection(target);
      target.multiplyScalar(10).add(camera.position);

      dispatch(setCameraPosition({
        position,
        target: target.toArray() as [number, number, number]
      }));
    }
  });

  // Handle mouse interactions
  const handlePointerDown = useCallback((event: any) => {
    if (previewMode) return;

    event.stopPropagation();
    setIsEditing(true);

    // Update raycaster using gl.domElement instead of event.target
    const rect = gl.domElement.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    raycaster.setFromCamera(mouse, camera);

    // Get intersections with scene objects
    const intersects = raycaster.intersectObjects(scene.children, true);

    if (intersects.length > 0) {
      const intersection = intersects[0];
      const point = intersection.point;

      switch (editorState.selectedTool) {
        case EditorTool.PLACE:
          handlePlaceAction(point);
          break;
        case EditorTool.SELECT:
          handleSelectAction(intersection);
          break;
        case EditorTool.ERASE:
          handleEraseAction(intersection);
          break;
      }
    }
  }, [
    previewMode,
    editorState.selectedTool,
    editorState.selectedAsset,
    editorState.mode,
    camera,
    raycaster,
    scene,
    gl
  ]);

  // Handle mouse up to stop editing
  const handlePointerUp = useCallback(() => {
    setIsEditing(false);
  }, []);

  // Add global mouse up listener
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setIsEditing(false);
    };

    window.addEventListener('mouseup', handleGlobalMouseUp);
    window.addEventListener('pointerup', handleGlobalMouseUp);

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
      window.removeEventListener('pointerup', handleGlobalMouseUp);
    };
  }, []);

  const handlePlaceAction = (point: THREE.Vector3) => {
    if (!currentMap) return;

    const snapPosition = editorState.snapToGrid
      ? snapToGrid(point, editorState.gridSize)
      : point;

    switch (editorState.mode) {
      case EditorMode.TILE_PLACEMENT:
        if (editorState.selectedAsset?.type === 'tile') {
          const tileType = getTileTypeFromAsset(editorState.selectedAsset.id);
          dispatch(addTile({
            position: [snapPosition.x, snapPosition.y, snapPosition.z],
            type: tileType
          }));
        }
        break;

      case EditorMode.ENVIRONMENT_OBJECTS:
        if (editorState.selectedAsset) {
          const newObject: PlacedObject = {
            id: `obj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            assetId: editorState.selectedAsset.id,
            position: [snapPosition.x, snapPosition.y, snapPosition.z],
            rotation: [...editorState.selectedAsset.defaultRotation],
            scale: [...editorState.selectedAsset.defaultScale],
            properties: {}
          };
          dispatch(addObject(newObject));
        }
        break;
    }
  };

  const handleSelectAction = (intersection: THREE.Intersection) => {
    const object = intersection.object;
    const userData = object.userData;

    if (userData.objectId) {
      dispatch(selectObjects([userData.objectId]));
      setSelectedTiles([]); // Clear tile selection when selecting objects
    } else if (userData.tileId) {
      // Handle tile selection
      const tileId = userData.tileId;
      setSelectedTiles([tileId]);
      dispatch(selectObjects([])); // Clear object selection when selecting tiles
      console.log('Selected tile:', tileId);
    }
  };

  const handleEraseAction = (intersection: THREE.Intersection) => {
    const object = intersection.object;
    const userData = object.userData;

    if (userData.objectId) {
      // dispatch(removeObject(userData.objectId));
    } else if (userData.tileId) {
      // dispatch(removeTile(userData.tileId));
    }
  };

  const snapToGrid = (point: THREE.Vector3, gridSize: number): THREE.Vector3 => {
    return new THREE.Vector3(
      Math.round(point.x / gridSize) * gridSize,
      Math.round(point.y / gridSize) * gridSize,
      Math.round(point.z / gridSize) * gridSize
    );
  };

  const getTileTypeFromAsset = (assetId: string): TileType => {
    switch (assetId) {
      case 'tile_healing': return TileType.HEALING;
      case 'tile_damage': return TileType.DAMAGE;
      case 'tile_key': return TileType.KEY;
      case 'tile_treasure_chest': return TileType.TREASURE_CHEST;
      default: return TileType.BASIC;
    }
  };

  const handleTransformChange = useCallback(() => {
    if (transformControlsRef.current && selectedObjects.length === 1) {
      const object = transformControlsRef.current.object;
      if (object && object.userData.objectId) {
        const position = object.position.toArray() as [number, number, number];
        const rotation = object.rotation.toArray().slice(0, 3) as [number, number, number];
        const scale = object.scale.toArray() as [number, number, number];

        dispatch(updateObject({
          id: object.userData.objectId,
          updates: { position, rotation, scale }
        }));
      }
    }
  }, [selectedObjects, dispatch]);

  // Effect to attach TransformControls to selected object
  useEffect(() => {
    if (transformControlsRef.current && selectedObjects.length === 1) {
      const selectedObjectId = selectedObjects[0];
      const selectedObjectData = currentMap?.placedObjects.find(obj => obj.id === selectedObjectId);

      if (selectedObjectData) {
        // Find the mesh in the scene
        scene.traverse((child) => {
          if (child.userData.objectId === selectedObjectId) {
            transformControlsRef.current.attach(child);
            return;
          }
        });
      }
    } else if (transformControlsRef.current) {
      transformControlsRef.current.detach();
    }
  }, [selectedObjects, scene, currentMap]);

  if (!currentMap) return null;

  return (
    <>
      {/* Simple Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 20, 10]} intensity={1.0} castShadow />

      {/* Camera Controls */}
      <OrbitControls
        ref={orbitControlsRef}
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={10}
        maxDistance={100}
        minPolarAngle={0}
        maxPolarAngle={Math.PI / 2}
        onStart={() => setIsDragging(true)}
        onEnd={() => setIsDragging(false)}
        enabled={!isEditing && !isDragging}
      />

      {/* Grid - Simple grid helper instead of EditorGrid */}
      {editorState.gridVisible && !previewMode && (
        <gridHelper args={[100, 100, '#888888', '#888888']} />
      )}

      {/* Ground Plane for Raycasting */}
      <mesh
        ref={groundPlaneRef}
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, -0.1, 0]}
        onPointerDown={handlePointerDown}
        visible={false}
      >
        <planeGeometry args={[200, 200]} />
        <meshBasicMaterial transparent opacity={0} />
      </mesh>

      {/* Terrain - Working terrain system */}
      <TerrainSystem
        configuration={currentMap.terrain}
        editable={!previewMode && editorState.mode === EditorMode.TERRAIN_SCULPTING}
      />

      {/* Game Tiles */}
      {currentMap.tiles.map((tile) => (
        <EditorTile
          key={tile.id}
          tile={tile}
          isSelected={selectedTiles.includes(tile.id)}
          isHovered={hoveredTile === tile.id}
          editable={!previewMode && editorState.mode === EditorMode.TILE_PLACEMENT}
          onSelect={() => {
            setSelectedTiles([tile.id]);
            dispatch(selectObjects([])); // Clear object selection
          }}
          onHover={(hovered) => setHoveredTile(hovered ? tile.id : null)}
        />
      ))}

      {/* Placed Objects */}
      {currentMap.placedObjects.map((object) => (
        <EditorObject
          key={object.id}
          object={object}
          isSelected={selectedObjects.includes(object.id)}
          isHovered={hoveredObject === object.id}
          editable={!previewMode && editorState.mode === EditorMode.ENVIRONMENT_OBJECTS}
          onSelect={() => dispatch(selectObjects([object.id]))}
          onHover={(hovered) => setHoveredObject(hovered ? object.id : null)}
        />
      ))}

      {/* Transform Controls */}
      {!previewMode && selectedObjects.length === 1 && (
        <TransformControls
          ref={transformControlsRef}
          mode={getTransformMode()}
          onObjectChange={handleTransformChange}
          onDragging={(dragging) => {
            setIsEditing(dragging);
            if (orbitControlsRef.current) {
              orbitControlsRef.current.enabled = !dragging;
            }
          }}
        />
      )}

      {/* Environment Effects */}
      {currentMap.environment.fog?.enabled && (
        <fog
          attach="fog"
          color={currentMap.environment.fog.color}
          near={currentMap.environment.fog.near}
          far={currentMap.environment.fog.far}
        />
      )}
    </>
  );

  function getTransformMode(): "translate" | "rotate" | "scale" {
    switch (editorState.selectedTool) {
      case EditorTool.MOVE:
        return 'translate';
      case EditorTool.ROTATE:
        return 'rotate';
      case EditorTool.SCALE:
        return 'scale';
      default:
        return 'translate';
    }
  }
};
