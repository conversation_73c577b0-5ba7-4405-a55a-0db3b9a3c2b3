import React, { useRef, useState, useCallback, useEffect } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { OrbitControls, TransformControls } from '@react-three/drei';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import {
  addTile,
  removeTile,
  updateTile,
  addObject,
  selectObjects,
  selectTiles,
  setCameraPosition,
  updateObject,
  removeObject
} from '../../store/editor';
import { EditorMode, EditorTool, PlacedObject } from '../../types/editorTypes';
import { TileType } from '../../types/gameTypes';
import { EditorTile } from './EditorTile';
import { EditorObject } from './EditorObject';
import { EditorGrid } from './EditorGrid';
import { TerrainSystem } from './TerrainSystem';
import * as THREE from 'three';

export const EditorScene: React.FC = () => {
  const dispatch = useDispatch();
  const { camera, raycaster, scene, gl } = useThree();
  const {
    currentMap,
    editorState,
    selectedObjects,
    previewMode
  } = useSelector((state: RootState) => state.editor);

  const selectedTiles = editorState.selectedTiles;

  const [hoveredObject, setHoveredObject] = useState<string | null>(null);
  const [hoveredTile, setHoveredTile] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [mousePosition, setMousePosition] = useState(new THREE.Vector2());
  const mouse = useRef(new THREE.Vector2());
  const transformControlsRef = useRef<any>(null);
  const orbitControlsRef = useRef<any>(null);
  const groundPlaneRef = useRef<THREE.Mesh>(null);
  const terrainRef = useRef<THREE.Mesh>(null);

  // Update camera position in store when it changes
  useFrame(() => {
    if (camera && !isDragging) {
      const position = camera.position.toArray() as [number, number, number];
      const target = new THREE.Vector3();
      camera.getWorldDirection(target);
      target.multiplyScalar(10).add(camera.position);

      dispatch(setCameraPosition({
        position,
        target: target.toArray() as [number, number, number]
      }));
    }
  });

  // Handle mouse interactions
  const handlePointerDown = useCallback((event: any) => {
    if (previewMode) return;

    event.stopPropagation();

    // Update raycaster using gl.domElement instead of event.target
    const rect = gl.domElement.getBoundingClientRect();
    mouse.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouse.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    raycaster.setFromCamera(mouse.current, camera);

    // Get intersections with scene objects
    const intersects = raycaster.intersectObjects(scene.children, true);

    if (intersects.length > 0) {
      const intersection = intersects[0];
      const point = intersection.point;

      switch (editorState.selectedTool) {
        case EditorTool.PLACE:
          handlePlaceAction(point);
          break;
        case EditorTool.SELECT:
          handleSelectAction(intersection);
          break;
        case EditorTool.ERASE:
          handleEraseAction(intersection);
          break;
      }
    }
  }, [
    previewMode,
    editorState.selectedTool,
    editorState.selectedAsset,
    editorState.mode,
    camera,
    raycaster,
    scene,
    gl
  ]);

  const handlePlaceAction = (point: THREE.Vector3) => {
    if (!currentMap) return;

    const snapPosition = editorState.snapToGrid
      ? snapToGrid(point, editorState.gridSize)
      : point;

    switch (editorState.mode) {
      case EditorMode.TILE_PLACEMENT:
        if (editorState.selectedAsset?.type === 'tile') {
          const tileType = getTileTypeFromAsset(editorState.selectedAsset.id);
          dispatch(addTile({
            position: [snapPosition.x, snapPosition.y, snapPosition.z],
            type: tileType
          }));
        }
        break;

      case EditorMode.ENVIRONMENT_OBJECTS:
        if (editorState.selectedAsset) {
          const newObject: PlacedObject = {
            id: `obj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            assetId: editorState.selectedAsset.id,
            position: [snapPosition.x, snapPosition.y, snapPosition.z],
            rotation: [...editorState.selectedAsset.defaultRotation],
            scale: [...editorState.selectedAsset.defaultScale],
            properties: {}
          };
          dispatch(addObject(newObject));
        }
        break;
    }
  };

  const handleSelectAction = (intersection: THREE.Intersection) => {
    const object = intersection.object;
    const userData = object.userData;

    if (userData.objectId) {
      dispatch(selectObjects([userData.objectId]));
      dispatch(selectTiles([])); // Clear tile selection when selecting objects
    } else if (userData.tileId) {
      // Handle tile selection
      const tileId = userData.tileId;
      dispatch(selectTiles([tileId]));
      dispatch(selectObjects([])); // Clear object selection when selecting tiles
      console.log('Selected tile:', tileId);
    }
  };

  const handleEraseAction = (intersection: THREE.Intersection) => {
    const object = intersection.object;
    const userData = object.userData;

    if (userData.objectId) {
      // Remove environment object
      dispatch(removeObject(userData.objectId));
      // Clear selection if this object was selected
      if (selectedObjects.includes(userData.objectId)) {
        dispatch(selectObjects([]));
      }
      console.log('Removed object:', userData.objectId);
    } else if (userData.tileId) {
      // Remove tile
      dispatch(removeTile(userData.tileId));
      // Clear tile selection if this tile was selected
      if (selectedTiles.includes(userData.tileId)) {
        dispatch(selectTiles([]));
      }
      console.log('Removed tile:', userData.tileId);
    }
  };

  const snapToGrid = (point: THREE.Vector3, gridSize: number): THREE.Vector3 => {
    return new THREE.Vector3(
      Math.round(point.x / gridSize) * gridSize,
      Math.round(point.y / gridSize) * gridSize,
      Math.round(point.z / gridSize) * gridSize
    );
  };

  const getTileTypeFromAsset = (assetId: string): TileType => {
    switch (assetId) {
      case 'tile_healing': return TileType.HEALING;
      case 'tile_damage': return TileType.DAMAGE;
      case 'tile_key': return TileType.KEY;
      case 'tile_treasure_chest': return TileType.TREASURE_CHEST;
      default: return TileType.BASIC;
    }
  };

  const handleTransformChange = useCallback(() => {
    if (transformControlsRef.current) {
      const transformedObject = transformControlsRef.current.object;

      // Handle object transformation
      if (transformedObject && transformedObject.userData.objectId && selectedObjects.length === 1) {
        let position = transformedObject.position.toArray() as [number, number, number];
        const rotation = transformedObject.rotation.toArray().slice(0, 3) as [number, number, number];
        const scale = transformedObject.scale.toArray() as [number, number, number];

        // Apply snap to grid if enabled
        if (editorState.snapToGrid) {
          const snappedPos = snapToGrid(new THREE.Vector3(...position), editorState.gridSize);
          position = [snappedPos.x, snappedPos.y, snappedPos.z];
          transformedObject.position.set(snappedPos.x, snappedPos.y, snappedPos.z);
        }

        // Update store immediately
        dispatch(updateObject({
          id: transformedObject.userData.objectId,
          updates: { position, rotation, scale }
        }));
      }
      // Handle tile transformation
      else if (transformedObject && transformedObject.userData.tileId && selectedTiles.length === 1) {
        let position = transformedObject.position.toArray() as [number, number, number];
        const rotation = transformedObject.rotation.toArray().slice(0, 3) as [number, number, number];
        const scale = transformedObject.scale.toArray() as [number, number, number];

        // Apply snap to grid if enabled
        if (editorState.snapToGrid) {
          const snappedPos = snapToGrid(new THREE.Vector3(...position), editorState.gridSize);
          position = [snappedPos.x, snappedPos.y, snappedPos.z];
          transformedObject.position.set(snappedPos.x, snappedPos.y, snappedPos.z);
        }

        // Update tile in store
        dispatch(updateTile({
          id: transformedObject.userData.tileId,
          updates: {
            position,
            customProperties: {
              rotation,
              scale
            }
          }
        }));
      }
    }
  }, [selectedObjects, selectedTiles, dispatch, editorState.snapToGrid, editorState.gridSize]);

  // Effect to attach TransformControls to selected object or tile
  useEffect(() => {
    if (transformControlsRef.current) {
      // Handle object selection
      if (selectedObjects.length === 1) {
        const selectedObjectId = selectedObjects[0];
        const selectedObjectData = currentMap?.placedObjects.find(obj => obj.id === selectedObjectId);

        if (selectedObjectData) {
          // Find the group (not mesh) in the scene that contains the object
          let targetGroup = null;
          scene.traverse((child) => {
            if (child.userData.objectId === selectedObjectId && child.type === 'Group') {
              targetGroup = child;
            }
          });

          if (targetGroup) {
            transformControlsRef.current.attach(targetGroup);
          }
        }
      }
      // Handle tile selection
      else if (selectedTiles.length === 1) {
        const selectedTileId = selectedTiles[0];
        const selectedTileData = currentMap?.tiles.find(tile => tile.id === selectedTileId);

        if (selectedTileData) {
          // Find the group that contains the tile
          let targetGroup = null;
          scene.traverse((child) => {
            if (child.userData.tileId === selectedTileId && child.type === 'Group') {
              targetGroup = child;
            }
          });

          if (targetGroup) {
            transformControlsRef.current.attach(targetGroup);
          }
        }
      }
      // No selection - detach controls
      else {
        transformControlsRef.current.detach();
      }
    }
  }, [selectedObjects, selectedTiles, scene, currentMap]);

  // Clear selections when entering terrain sculpting mode
  useEffect(() => {
    if (editorState.mode === EditorMode.TERRAIN_SCULPTING) {
      if (selectedObjects.length > 0) {
        dispatch(selectObjects([]));
      }
      if (selectedTiles.length > 0) {
        dispatch(selectTiles([]));
      }
    }
  }, [editorState.mode, selectedObjects.length, selectedTiles.length, dispatch]);

  if (!currentMap) return null;

  return (
    <>
      {/* Simple Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 20, 10]} intensity={1.0} castShadow />

      {/* Camera Controls
          - Middle Mouse Button (wheel click + drag): Camera rotation/orbiting
          - Right Mouse Button (drag): Camera panning
          - Mouse Wheel (scroll): Camera zoom in/out
          - Left Mouse Button: Free for editor tools (select, place, erase, etc.)
      */}
      <OrbitControls
        ref={orbitControlsRef}
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={10}
        maxDistance={100}
        minPolarAngle={0}
        maxPolarAngle={Math.PI / 2}
        mouseButtons={{
          LEFT: null, // Disable left mouse button - reserved for editor tools
          MIDDLE: THREE.MOUSE.ROTATE, // Middle mouse button for camera rotation
          RIGHT: THREE.MOUSE.PAN      // Right mouse button for camera panning
        }}
        onStart={() => setIsDragging(true)}
        onEnd={() => setIsDragging(false)}
      />

      {/* Grid - Dynamic grid based on editor settings */}
      {editorState.gridVisible && !previewMode && (
        <gridHelper
          args={[
            100, // Total size
            Math.floor(100 / editorState.gridSize), // Number of divisions based on grid size
            '#888888', // Center line color
            '#666666'  // Grid line color
          ]}
        />
      )}

      {/* Ground Plane for Raycasting */}
      <mesh
        ref={groundPlaneRef}
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, -0.1, 0]}
        onPointerDown={handlePointerDown}
        visible={false}
      >
        <planeGeometry args={[200, 200]} />
        <meshBasicMaterial transparent opacity={0} />
      </mesh>

      {/* Terrain - Working terrain system */}
      <TerrainSystem
        configuration={currentMap.terrain}
        editable={!previewMode && editorState.mode === EditorMode.TERRAIN_SCULPTING}
      />

      {/* Game Tiles */}
      {currentMap.tiles.map((tile) => (
        <EditorTile
          key={tile.id}
          tile={tile}
          isSelected={selectedTiles.includes(tile.id)}
          isHovered={hoveredTile === tile.id}
          editable={!previewMode && editorState.mode === EditorMode.TILE_PLACEMENT}
          isEraseMode={editorState.selectedTool === EditorTool.ERASE}
          onSelect={() => {
            // Only allow tile selection in TILE_PLACEMENT mode
            if (editorState.mode === EditorMode.TILE_PLACEMENT) {
              dispatch(selectTiles([tile.id]));
              dispatch(selectObjects([])); // Clear object selection
            }
          }}
          onHover={(hovered) => setHoveredTile(hovered ? tile.id : null)}
        />
      ))}

      {/* Placed Objects */}
      {currentMap.placedObjects.map((object) => (
        <EditorObject
          key={object.id}
          object={object}
          isSelected={selectedObjects.includes(object.id)}
          isHovered={hoveredObject === object.id}
          editable={!previewMode && editorState.mode === EditorMode.ENVIRONMENT_OBJECTS}
          isEraseMode={editorState.selectedTool === EditorTool.ERASE}
          onSelect={() => {
            // Only allow object selection in ENVIRONMENT_OBJECTS mode
            if (editorState.mode === EditorMode.ENVIRONMENT_OBJECTS) {
              dispatch(selectObjects([object.id]));
            }
          }}
          onHover={(hovered) => setHoveredObject(hovered ? object.id : null)}
        />
      ))}

      {/* Transform Controls - Only show when not in terrain sculpting mode */}
      {!previewMode &&
       editorState.mode !== EditorMode.TERRAIN_SCULPTING &&
       (selectedObjects.length === 1 || selectedTiles.length === 1) && (
        <TransformControls
          ref={transformControlsRef}
          mode={getTransformMode()}
          onObjectChange={handleTransformChange}
          onDragging={(dragging) => {
            if (orbitControlsRef.current) {
              orbitControlsRef.current.enabled = !dragging;
            }
          }}
        />
      )}

      {/* Environment Effects */}
      {currentMap.environment.fog?.enabled && (
        <fog
          attach="fog"
          color={currentMap.environment.fog.color}
          near={currentMap.environment.fog.near}
          far={currentMap.environment.fog.far}
        />
      )}
    </>
  );

  function getTransformMode(): "translate" | "rotate" | "scale" {
    switch (editorState.selectedTool) {
      case EditorTool.MOVE:
        return 'translate';
      case EditorTool.ROTATE:
        return 'rotate';
      case EditorTool.SCALE:
        return 'scale';
      default:
        return 'translate';
    }
  }
};
