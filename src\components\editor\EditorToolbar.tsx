import React from 'react';
import { EditorMode, EditorTool } from '../../types/editorTypes';
import './EditorToolbar.scss';

interface EditorToolbarProps {
  currentMode: EditorMode;
  currentTool: EditorTool;
  onModeChange: (mode: EditorMode) => void;
  onToolChange: (tool: EditorTool) => void;
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  gridVisible: boolean;
  snapToGrid: boolean;
  onToggleGrid: () => void;
  onToggleSnap: () => void;
}

export const EditorToolbar: React.FC<EditorToolbarProps> = ({
  currentMode,
  currentTool,
  onModeChange,
  onToolChange,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  gridVisible,
  snapToGrid,
  onToggleGrid,
  onToggleSnap,
}) => {
  const modes = [
    { 
      id: EditorMode.TILE_PLACEMENT, 
      name: 'Tiles', 
      icon: '🎯', 
      description: 'Place and edit game tiles' 
    },
    { 
      id: EditorMode.TERRAIN_SCULPTING, 
      name: 'Terrain', 
      icon: '🏔️', 
      description: 'Sculpt and modify terrain' 
    },
    { 
      id: EditorMode.ENVIRONMENT_OBJECTS, 
      name: 'Objects', 
      icon: '🌵', 
      description: 'Place environmental objects' 
    },
    { 
      id: EditorMode.LIGHTING, 
      name: 'Lighting', 
      icon: '💡', 
      description: 'Configure lighting and atmosphere' 
    },
    { 
      id: EditorMode.PREVIEW, 
      name: 'Preview', 
      icon: '👁️', 
      description: 'Preview the final map' 
    },
  ];

  const getToolsForMode = (mode: EditorMode): Array<{ id: EditorTool; name: string; icon: string; description: string }> => {
    const commonTools = [
      { id: EditorTool.SELECT, name: 'Select', icon: '🔍', description: 'Select objects (V)' },
    ];

    switch (mode) {
      case EditorMode.TILE_PLACEMENT:
        return [
          ...commonTools,
          { id: EditorTool.PLACE, name: 'Place', icon: '➕', description: 'Place tiles (B)' },
          { id: EditorTool.PAINT, name: 'Paint', icon: '🎨', description: 'Paint tile types' },
          { id: EditorTool.ERASE, name: 'Erase', icon: '🗑️', description: 'Remove tiles (E)' },
        ];
      
      case EditorMode.TERRAIN_SCULPTING:
        return [
          ...commonTools,
          { id: EditorTool.TERRAIN_RAISE, name: 'Raise', icon: '⬆️', description: 'Raise terrain' },
          { id: EditorTool.TERRAIN_LOWER, name: 'Lower', icon: '⬇️', description: 'Lower terrain' },
          { id: EditorTool.TERRAIN_SMOOTH, name: 'Smooth', icon: '〰️', description: 'Smooth terrain' },
          { id: EditorTool.TERRAIN_FLATTEN, name: 'Flatten', icon: '▬', description: 'Flatten terrain' },
        ];
      
      case EditorMode.ENVIRONMENT_OBJECTS:
        return [
          ...commonTools,
          { id: EditorTool.PLACE, name: 'Place', icon: '➕', description: 'Place objects (B)' },
          { id: EditorTool.MOVE, name: 'Move', icon: '↔️', description: 'Move objects' },
          { id: EditorTool.ROTATE, name: 'Rotate', icon: '🔄', description: 'Rotate objects (R)' },
          { id: EditorTool.SCALE, name: 'Scale', icon: '📏', description: 'Scale objects (S)' },
          { id: EditorTool.ERASE, name: 'Erase', icon: '🗑️', description: 'Remove objects (E)' },
        ];
      
      case EditorMode.LIGHTING:
        return [
          ...commonTools,
          { id: EditorTool.PLACE, name: 'Place', icon: '➕', description: 'Place lights (B)' },
          { id: EditorTool.MOVE, name: 'Move', icon: '↔️', description: 'Move lights' },
          { id: EditorTool.ROTATE, name: 'Rotate', icon: '🔄', description: 'Rotate lights (R)' },
        ];
      
      default:
        return commonTools;
    }
  };

  const availableTools = getToolsForMode(currentMode);

  return (
    <div className="editor-toolbar">
      {/* Mode Selection */}
      <div className="toolbar-section">
        <div className="toolbar-label">Mode</div>
        <div className="toolbar-group">
          {modes.map(mode => (
            <button
              key={mode.id}
              className={`toolbar-btn ${currentMode === mode.id ? 'active' : ''}`}
              onClick={() => onModeChange(mode.id)}
              title={mode.description}
            >
              <span className="btn-icon">{mode.icon}</span>
              <span className="btn-text">{mode.name}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="toolbar-separator" />

      {/* Tool Selection */}
      <div className="toolbar-section">
        <div className="toolbar-label">Tools</div>
        <div className="toolbar-group">
          {availableTools.map(tool => (
            <button
              key={tool.id}
              className={`toolbar-btn ${currentTool === tool.id ? 'active' : ''}`}
              onClick={() => onToolChange(tool.id)}
              title={tool.description}
            >
              <span className="btn-icon">{tool.icon}</span>
              <span className="btn-text">{tool.name}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="toolbar-separator" />

      {/* History Controls */}
      <div className="toolbar-section">
        <div className="toolbar-label">History</div>
        <div className="toolbar-group">
          <button
            className="toolbar-btn"
            onClick={onUndo}
            disabled={!canUndo}
            title="Undo (Ctrl+Z)"
          >
            <span className="btn-icon">↶</span>
            <span className="btn-text">Undo</span>
          </button>
          <button
            className="toolbar-btn"
            onClick={onRedo}
            disabled={!canRedo}
            title="Redo (Ctrl+Shift+Z)"
          >
            <span className="btn-icon">↷</span>
            <span className="btn-text">Redo</span>
          </button>
        </div>
      </div>

      <div className="toolbar-separator" />

      {/* Grid Controls */}
      <div className="toolbar-section">
        <div className="toolbar-label">Grid</div>
        <div className="toolbar-group">
          <button
            className={`toolbar-btn ${gridVisible ? 'active' : ''}`}
            onClick={onToggleGrid}
            title="Toggle Grid (Ctrl+G)"
          >
            <span className="btn-icon">⊞</span>
            <span className="btn-text">Grid</span>
          </button>
          <button
            className={`toolbar-btn ${snapToGrid ? 'active' : ''}`}
            onClick={onToggleSnap}
            title="Toggle Snap to Grid"
          >
            <span className="btn-icon">🧲</span>
            <span className="btn-text">Snap</span>
          </button>
        </div>
      </div>

      {/* Spacer */}
      <div className="toolbar-spacer" />

      {/* Quick Actions */}
      <div className="toolbar-section">
        <div className="toolbar-group">
          <div className="toolbar-info">
            <span className="info-label">Mode:</span>
            <span className="info-value">{currentMode.replace('_', ' ')}</span>
          </div>
          <div className="toolbar-info">
            <span className="info-label">Tool:</span>
            <span className="info-value">{currentTool}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
