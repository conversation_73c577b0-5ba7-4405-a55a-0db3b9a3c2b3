import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { 
  EditorState, 
  EditorMode, 
  EditorTool, 
  Asset, 
  MapConfiguration, 
  PlacedObject,
  EditorAction,
  EditorHistory
} from '../types/editorTypes';
import { TileType } from '../types/gameTypes';

interface EditorSliceState {
  currentMap: MapConfiguration | null;
  editorState: EditorState;
  history: EditorHistory;
  assets: Asset[];
  selectedObjects: string[];
  clipboard: PlacedObject[];
  isLoading: boolean;
  error: string | null;
  isDirty: boolean;
  previewMode: boolean;
}

const initialEditorState: EditorState = {
  mode: EditorMode.TILE_PLACEMENT,
  selectedTool: EditorTool.SELECT,
  selectedAsset: null,
  selectedTiles: [],
  cameraPosition: [0, 35, 35],
  cameraTarget: [0, 0, 0],
  gridVisible: true,
  snapToGrid: true,
  gridSize: 1,
};

const initialState: EditorSliceState = {
  currentMap: null,
  editorState: initialEditorState,
  history: {
    actions: [],
    currentIndex: -1,
    maxHistory: 50,
  },
  assets: [],
  selectedObjects: [],
  clipboard: [],
  isLoading: false,
  error: null,
  isDirty: false,
  previewMode: false,
};

const editorSlice = createSlice({
  name: 'editor',
  initialState,
  reducers: {
    // Map Management
    createNewMap: (state, action: PayloadAction<{ name: string; boardSize: number }>) => {
      const { name, boardSize } = action.payload;
      state.currentMap = {
        id: `map_${Date.now()}`,
        name,
        description: '',
        boardSize,
        treasureChestPosition: 15,
        keysToWin: 40,
        tiles: [],
        terrain: {
          baseRadius: 32,
          radiusVariation: 12,
          heightVariation: 2,
          layers: [],
          customMeshes: [],
        },
        environment: {
          skybox: {
            type: 'gradient',
            topColor: '#87CEEB',
            bottomColor: '#F4A460',
          },
          fog: {
            enabled: true,
            color: '#F4A460',
            near: 50,
            far: 200,
          },
        },
        lighting: {
          ambientLight: {
            color: '#ffffff',
            intensity: 0.6,
          },
          directionalLight: {
            color: '#ffffff',
            intensity: 1.0,
            position: [10, 20, 10],
            castShadow: true,
            shadowMapSize: 2048,
          },
          additionalLights: [],
        },
        placedObjects: [],
        metadata: {
          created: Date.now(),
          modified: Date.now(),
          version: '1.0.0',
        },
      };
      state.isDirty = false;
      state.history.actions = [];
      state.history.currentIndex = -1;
    },

    loadMap: (state, action: PayloadAction<MapConfiguration>) => {
      state.currentMap = action.payload;
      state.isDirty = false;
      state.history.actions = [];
      state.history.currentIndex = -1;
    },

    saveMap: (state) => {
      if (state.currentMap) {
        state.currentMap.metadata.modified = Date.now();
        state.isDirty = false;
      }
    },

    // Editor State
    setEditorMode: (state, action: PayloadAction<EditorMode>) => {
      state.editorState.mode = action.payload;
      state.editorState.selectedTool = EditorTool.SELECT; // Reset tool when changing mode
    },

    setEditorTool: (state, action: PayloadAction<EditorTool>) => {
      state.editorState.selectedTool = action.payload;
    },

    setSelectedAsset: (state, action: PayloadAction<Asset | null>) => {
      state.editorState.selectedAsset = action.payload;
    },

    setCameraPosition: (state, action: PayloadAction<{ position: [number, number, number]; target: [number, number, number] }>) => {
      state.editorState.cameraPosition = action.payload.position;
      state.editorState.cameraTarget = action.payload.target;
    },

    toggleGrid: (state) => {
      state.editorState.gridVisible = !state.editorState.gridVisible;
    },

    toggleSnapToGrid: (state) => {
      state.editorState.snapToGrid = !state.editorState.snapToGrid;
    },

    setGridSize: (state, action: PayloadAction<number>) => {
      state.editorState.gridSize = action.payload;
    },

    // Object Selection
    selectObjects: (state, action: PayloadAction<string[]>) => {
      state.selectedObjects = action.payload;
    },

    addToSelection: (state, action: PayloadAction<string>) => {
      if (!state.selectedObjects.includes(action.payload)) {
        state.selectedObjects.push(action.payload);
      }
    },

    removeFromSelection: (state, action: PayloadAction<string>) => {
      state.selectedObjects = state.selectedObjects.filter(id => id !== action.payload);
    },

    clearSelection: (state) => {
      state.selectedObjects = [];
    },

    // Tile Management
    addTile: (state, action: PayloadAction<{ position: [number, number, number]; type: TileType }>) => {
      if (!state.currentMap) return;
      
      const newTile = {
        id: state.currentMap.tiles.length + 1,
        type: action.payload.type,
        position: action.payload.position,
      };
      
      state.currentMap.tiles.push(newTile);
      state.isDirty = true;
      
      // Add to history
      const historyAction: EditorAction = {
        type: 'ADD_TILE',
        payload: newTile,
        timestamp: Date.now(),
        description: `Added ${action.payload.type} tile`,
      };
      editorSlice.caseReducers.addToHistory(state, { payload: historyAction, type: 'addToHistory' });
    },

    removeTile: (state, action: PayloadAction<number>) => {
      if (!state.currentMap) return;
      
      const tileIndex = state.currentMap.tiles.findIndex(tile => tile.id === action.payload);
      if (tileIndex !== -1) {
        const removedTile = state.currentMap.tiles[tileIndex];
        state.currentMap.tiles.splice(tileIndex, 1);
        state.isDirty = true;
        
        // Add to history
        const historyAction: EditorAction = {
          type: 'REMOVE_TILE',
          payload: { tile: removedTile, index: tileIndex },
          timestamp: Date.now(),
          description: `Removed ${removedTile.type} tile`,
        };
        editorSlice.caseReducers.addToHistory(state, { payload: historyAction, type: 'addToHistory' });
      }
    },

    updateTile: (state, action: PayloadAction<{ id: number; updates: Partial<any> }>) => {
      if (!state.currentMap) return;
      
      const tile = state.currentMap.tiles.find(t => t.id === action.payload.id);
      if (tile) {
        Object.assign(tile, action.payload.updates);
        state.isDirty = true;
      }
    },

    // Object Management
    addObject: (state, action: PayloadAction<PlacedObject>) => {
      if (!state.currentMap) return;
      
      state.currentMap.placedObjects.push(action.payload);
      state.isDirty = true;
      
      const historyAction: EditorAction = {
        type: 'ADD_OBJECT',
        payload: action.payload,
        timestamp: Date.now(),
        description: `Added object ${action.payload.assetId}`,
      };
      editorSlice.caseReducers.addToHistory(state, { payload: historyAction, type: 'addToHistory' });
    },

    removeObject: (state, action: PayloadAction<string>) => {
      if (!state.currentMap) return;
      
      const objectIndex = state.currentMap.placedObjects.findIndex(obj => obj.id === action.payload);
      if (objectIndex !== -1) {
        const removedObject = state.currentMap.placedObjects[objectIndex];
        state.currentMap.placedObjects.splice(objectIndex, 1);
        state.selectedObjects = state.selectedObjects.filter(id => id !== action.payload);
        state.isDirty = true;
        
        const historyAction: EditorAction = {
          type: 'REMOVE_OBJECT',
          payload: { object: removedObject, index: objectIndex },
          timestamp: Date.now(),
          description: `Removed object ${removedObject.assetId}`,
        };
        editorSlice.caseReducers.addToHistory(state, { payload: historyAction, type: 'addToHistory' });
      }
    },

    updateObject: (state, action: PayloadAction<{ id: string; updates: Partial<PlacedObject> }>) => {
      if (!state.currentMap) return;
      
      const object = state.currentMap.placedObjects.find(obj => obj.id === action.payload.id);
      if (object) {
        Object.assign(object, action.payload.updates);
        state.isDirty = true;
      }
    },

    // History Management
    addToHistory: (state, action: PayloadAction<EditorAction>) => {
      // Remove any actions after current index (when undoing and then doing new action)
      state.history.actions = state.history.actions.slice(0, state.history.currentIndex + 1);
      
      // Add new action
      state.history.actions.push(action.payload);
      state.history.currentIndex++;
      
      // Limit history size
      if (state.history.actions.length > state.history.maxHistory) {
        state.history.actions.shift();
        state.history.currentIndex--;
      }
    },

    undo: (state) => {
      if (state.history.currentIndex >= 0) {
        const action = state.history.actions[state.history.currentIndex];
        // Apply reverse of the action (implementation would depend on action type)
        state.history.currentIndex--;
        state.isDirty = true;
      }
    },

    redo: (state) => {
      if (state.history.currentIndex < state.history.actions.length - 1) {
        state.history.currentIndex++;
        const action = state.history.actions[state.history.currentIndex];
        // Reapply the action (implementation would depend on action type)
        state.isDirty = true;
      }
    },

    // Asset Management
    setAssets: (state, action: PayloadAction<Asset[]>) => {
      state.assets = action.payload;
    },

    // Clipboard Operations
    copyToClipboard: (state) => {
      if (!state.currentMap) return;
      
      state.clipboard = state.currentMap.placedObjects.filter(obj => 
        state.selectedObjects.includes(obj.id)
      );
    },

    pasteFromClipboard: (state, action: PayloadAction<[number, number, number]>) => {
      if (!state.currentMap || state.clipboard.length === 0) return;
      
      const offset = action.payload;
      const newObjects = state.clipboard.map(obj => ({
        ...obj,
        id: `${obj.id}_copy_${Date.now()}`,
        position: [
          obj.position[0] + offset[0],
          obj.position[1] + offset[1],
          obj.position[2] + offset[2],
        ] as [number, number, number],
      }));
      
      state.currentMap.placedObjects.push(...newObjects);
      state.selectedObjects = newObjects.map(obj => obj.id);
      state.isDirty = true;
    },

    // UI State
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    togglePreviewMode: (state) => {
      state.previewMode = !state.previewMode;
    },
  },
});

export const {
  createNewMap,
  loadMap,
  saveMap,
  setEditorMode,
  setEditorTool,
  setSelectedAsset,
  setCameraPosition,
  toggleGrid,
  toggleSnapToGrid,
  setGridSize,
  selectObjects,
  addToSelection,
  removeFromSelection,
  clearSelection,
  addTile,
  removeTile,
  updateTile,
  addObject,
  removeObject,
  updateObject,
  addToHistory,
  undo,
  redo,
  setAssets,
  copyToClipboard,
  pasteFromClipboard,
  setLoading,
  setError,
  togglePreviewMode,
} = editorSlice.actions;

export default editorSlice.reducer;
