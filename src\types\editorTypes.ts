import { TileType } from './gameTypes';

export interface EditorState {
  mode: EditorMode;
  selectedTool: EditorTool;
  selectedAsset: Asset | null;
  selectedTiles: number[];
  cameraPosition: [number, number, number];
  cameraTarget: [number, number, number];
  gridVisible: boolean;
  snapToGrid: boolean;
  gridSize: number;
}

export enum EditorMode {
  TILE_PLACEMENT = 'tile_placement',
  TERRAIN_SCULPTING = 'terrain_sculpting',
  ENVIRONMENT_OBJECTS = 'environment_objects',
  LIGHTING = 'lighting',
  PREVIEW = 'preview'
}

export enum EditorTool {
  SELECT = 'select',
  PLACE = 'place',
  PAINT = 'paint',
  ERASE = 'erase',
  MOVE = 'move',
  ROTATE = 'rotate',
  SCALE = 'scale',
  TERRAIN_RAISE = 'terrain_raise',
  TERRAIN_LOWER = 'terrain_lower',
  TERRAIN_SMOOTH = 'terrain_smooth',
  TERRAIN_FLATTEN = 'terrain_flatten'
}

export enum AssetCategory {
  TILES = 'tiles',
  TERRAIN = 'terrain',
  ROCKS = 'rocks',
  VEGETATION = 'vegetation',
  STRUCTURES = 'structures',
  DECORATIVE = 'decorative',
  LIGHTING = 'lighting'
}

export interface Asset {
  id: string;
  name: string;
  category: AssetCategory;
  type: AssetType;
  thumbnail: string;
  modelPath?: string;
  previewComponent?: React.ComponentType<any>;
  defaultScale: [number, number, number];
  defaultRotation: [number, number, number];
  boundingBox: {
    width: number;
    height: number;
    depth: number;
  };
  tags: string[];
  description?: string;
}

export enum AssetType {
  TILE = 'tile',
  TERRAIN_MESH = 'terrain_mesh',
  STATIC_MESH = 'static_mesh',
  PROCEDURAL = 'procedural',
  PARTICLE_SYSTEM = 'particle_system',
  LIGHT = 'light'
}

export interface PlacedObject {
  id: string;
  assetId: string;
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
  properties: Record<string, any>;
}

export interface MapConfiguration {
  id: string;
  name: string;
  description: string;
  boardSize: number;
  treasureChestPosition: number;
  keysToWin: number;
  tiles: CustomTile[];
  terrain: TerrainConfiguration;
  environment: EnvironmentConfiguration;
  lighting: LightingConfiguration;
  placedObjects: PlacedObject[];
  metadata: {
    created: number;
    modified: number;
    version: string;
    author?: string;
  };
}

export interface CustomTile {
  id: number;
  type: TileType;
  position: [number, number, number];
  customProperties?: {
    heightOffset?: number;
    scale?: [number, number, number];
    rotation?: [number, number, number];
    material?: {
      color?: string;
      texture?: string;
      emissive?: string;
      roughness?: number;
      metalness?: number;
    };
  };
}

export interface TerrainConfiguration {
  baseRadius: number;
  radiusVariation: number;
  heightVariation: number;
  layers: TerrainLayer[];
  customMeshes: PlacedObject[];
}

export interface TerrainLayer {
  id: string;
  name: string;
  material: {
    color: string;
    texture?: string;
    normalMap?: string;
    roughness: number;
    metalness: number;
  };
  heightRange: [number, number];
  blendMode: 'replace' | 'add' | 'multiply' | 'overlay';
}

export interface EnvironmentConfiguration {
  skybox?: {
    type: 'color' | 'gradient' | 'hdri';
    color?: string;
    topColor?: string;
    bottomColor?: string;
    hdriPath?: string;
  };
  fog?: {
    enabled: boolean;
    color: string;
    near: number;
    far: number;
    density?: number;
  };
  wind?: {
    enabled: boolean;
    direction: [number, number, number];
    strength: number;
  };
}

export interface LightingConfiguration {
  ambientLight: {
    color: string;
    intensity: number;
  };
  directionalLight: {
    color: string;
    intensity: number;
    position: [number, number, number];
    castShadow: boolean;
    shadowMapSize: number;
  };
  additionalLights: PlacedObject[];
}

export interface EditorAction {
  type: string;
  payload: any;
  timestamp: number;
  description: string;
}

export interface EditorHistory {
  actions: EditorAction[];
  currentIndex: number;
  maxHistory: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  type: 'tile_connectivity' | 'missing_treasure_chest' | 'invalid_tile_count' | 'asset_missing';
  message: string;
  affectedObjects: string[];
  severity: 'error' | 'warning';
}

export interface ValidationWarning {
  type: 'performance' | 'gameplay' | 'visual';
  message: string;
  suggestion?: string;
  affectedObjects: string[];
}

export interface ExportOptions {
  format: 'boardgame_io' | 'json' | 'gltf';
  includeAssets: boolean;
  optimizeForPerformance: boolean;
  compressionLevel: number;
}
