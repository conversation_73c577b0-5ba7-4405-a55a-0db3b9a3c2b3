import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { setSelectedAsset } from '../../store/editor';
import { TileType } from '../../types/gameTypes';
import { Asset, AssetCategory, AssetType } from '../../types/editorTypes';
import './TileEditor.scss';

const TILE_ASSETS: Asset[] = [
  {
    id: 'tile_basic',
    name: 'Basic Tile',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_basic.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 1, depth: 3 },
    tags: ['basic', 'tile'],
    description: 'Standard game tile for player movement',
  },
  {
    id: 'tile_healing',
    name: 'Healing Tile',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_healing.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 1, depth: 3 },
    tags: ['healing', 'special'],
    description: 'Restores player health when landed on',
  },
  {
    id: 'tile_damage',
    name: 'Damage Tile',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_damage.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 1, depth: 3 },
    tags: ['damage', 'special'],
    description: 'Damages player when landed on',
  },
  {
    id: 'tile_key',
    name: 'Key Tile',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_key.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 1, depth: 3 },
    tags: ['key', 'special'],
    description: 'Grants keys to the player',
  },
  {
    id: 'tile_treasure_chest',
    name: 'Treasure Chest',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_treasure.png',
    defaultScale: [1, 1.5, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 2, depth: 3 },
    tags: ['treasure', 'win'],
    description: 'Win condition tile - requires keys to open',
  },
];

export const TileEditor: React.FC = () => {
  const dispatch = useDispatch();
  const { currentMap, editorState } = useSelector((state: RootState) => state.editor);
  const [selectedTileType, setSelectedTileType] = useState<TileType>(TileType.BASIC);

  if (!currentMap) return null;

  const handleTileTypeSelect = (tileType: TileType) => {
    setSelectedTileType(tileType);
    const asset = TILE_ASSETS.find(a => a.id === `tile_${tileType}`);
    if (asset) {
      dispatch(setSelectedAsset(asset));
    }
  };

  const getTileColor = (type: TileType): string => {
    switch (type) {
      case TileType.BASIC: return '#ecf0f1';
      case TileType.HEALING: return '#2ecc71';
      case TileType.DAMAGE: return '#e74c3c';
      case TileType.KEY: return '#f1c40f';
      case TileType.TREASURE_CHEST: return '#9b59b6';
      default: return '#ecf0f1';
    }
  };

  const getTileIcon = (type: TileType): string => {
    switch (type) {
      case TileType.BASIC: return '⚪';
      case TileType.HEALING: return '💚';
      case TileType.DAMAGE: return '💥';
      case TileType.KEY: return '🗝️';
      case TileType.TREASURE_CHEST: return '💎';
      default: return '⚪';
    }
  };

  const getTileStats = () => {
    const stats = {
      [TileType.BASIC]: 0,
      [TileType.HEALING]: 0,
      [TileType.DAMAGE]: 0,
      [TileType.KEY]: 0,
      [TileType.TREASURE_CHEST]: 0,
    };

    currentMap.tiles.forEach(tile => {
      stats[tile.type]++;
    });

    return stats;
  };

  const tileStats = getTileStats();
  const totalTiles = currentMap.tiles.length;

  return (
    <div className="tile-editor">
      <div className="tile-editor__header">
        <h3>Tile Placement</h3>
        <p>Select a tile type and click on the board to place it.</p>
      </div>

      {/* Tile Type Selection */}
      <div className="tile-types">
        <h4>Tile Types</h4>
        <div className="tile-type-grid">
          {Object.values(TileType).map(tileType => (
            <button
              key={tileType}
              className={`tile-type-btn ${selectedTileType === tileType ? 'selected' : ''}`}
              onClick={() => handleTileTypeSelect(tileType)}
              style={{ backgroundColor: getTileColor(tileType) }}
            >
              <div className="tile-type-icon">{getTileIcon(tileType)}</div>
              <div className="tile-type-name">{tileType.replace('_', ' ')}</div>
              <div className="tile-type-count">{tileStats[tileType]}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Tile Statistics */}
      <div className="tile-stats">
        <h4>Board Statistics</h4>
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-label">Total Tiles:</span>
            <span className="stat-value">{totalTiles}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Board Size:</span>
            <span className="stat-value">{currentMap.boardSize}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Completion:</span>
            <span className="stat-value">{Math.round((totalTiles / currentMap.boardSize) * 100)}%</span>
          </div>
        </div>

        <div className="tile-distribution">
          <h5>Tile Distribution</h5>
          {Object.entries(tileStats).map(([type, count]) => (
            <div key={type} className="distribution-item">
              <div className="distribution-bar">
                <div 
                  className="distribution-fill"
                  style={{ 
                    width: totalTiles > 0 ? `${(count / totalTiles) * 100}%` : '0%',
                    backgroundColor: getTileColor(type as TileType)
                  }}
                />
              </div>
              <span className="distribution-label">
                {getTileIcon(type as TileType)} {type.replace('_', ' ')}: {count}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Tile Layout Tools */}
      <div className="layout-tools">
        <h4>Layout Tools</h4>
        <div className="tool-buttons">
          <button className="tool-btn" title="Auto-generate sequential layout">
            🔄 Auto Layout
          </button>
          <button className="tool-btn" title="Clear all tiles">
            🗑️ Clear All
          </button>
          <button className="tool-btn" title="Randomize tile types">
            🎲 Randomize
          </button>
        </div>
      </div>

      {/* Tile Properties */}
      {editorState.selectedAsset && (
        <div className="tile-properties">
          <h4>Selected Tile Properties</h4>
          <div className="property-group">
            <label>Tile Type:</label>
            <span className="property-value">
              {getTileIcon(selectedTileType)} {selectedTileType.replace('_', ' ')}
            </span>
          </div>
          
          <div className="property-group">
            <label>Effect:</label>
            <span className="property-value">
              {editorState.selectedAsset.description}
            </span>
          </div>

          <div className="property-group">
            <label>Size:</label>
            <span className="property-value">
              {editorState.selectedAsset.boundingBox.width} × {editorState.selectedAsset.boundingBox.depth}
            </span>
          </div>
        </div>
      )}

      {/* Validation Warnings */}
      <div className="validation-warnings">
        {tileStats[TileType.TREASURE_CHEST] === 0 && (
          <div className="warning">
            ⚠️ No treasure chest tile placed. Add one to make the map playable.
          </div>
        )}
        {tileStats[TileType.TREASURE_CHEST] > 1 && (
          <div className="warning">
            ⚠️ Multiple treasure chests detected. Only one should be used.
          </div>
        )}
        {totalTiles < 20 && (
          <div className="warning">
            ⚠️ Very few tiles placed. Consider adding more for better gameplay.
          </div>
        )}
        {tileStats[TileType.KEY] === 0 && (
          <div className="info">
            💡 Consider adding key tiles to make collecting keys more strategic.
          </div>
        )}
      </div>
    </div>
  );
};
