import { Ctx, Game } from 'boardgame.io';
import { GAME_ID, GAME_CONFIG } from '../config';
import {
  GameState,
  Player,
  Tile,
  TileType,
  TileEffect
} from '../types/gameTypes';
import { rollDice, movePlayer, openTreasureChest } from './moves';

// Tile effect implementations
const TILE_EFFECTS: Record<TileType, TileEffect> = {
  [TileType.BASIC]: {
    type: TileType.BASIC,
    name: 'Basic Tile',
    description: 'No effect',
    apply: (player: Player) => player,
  },
  [TileType.HEALING]: {
    type: TileType.HEALING,
    name: 'Healing Tile',
    description: `Restores ${GAME_CONFIG.HEALING_AMOUNT} health`,
    apply: (player: Player) => ({
      ...player,
      health: Math.min(player.health + GAME_CONFIG.HEALING_AMOUNT, GAME_CONFIG.MAX_HEALTH),
    }),
  },
  [TileType.DAMAGE]: {
    type: TileType.DAMAGE,
    name: 'Damage Tile',
    description: `Deals ${GAME_CONFIG.DAMAGE_AMOUNT} damage`,
    apply: (player: Player) => ({
      ...player,
      health: Math.max(player.health - GAME_CONFIG.DAMAGE_AMOUNT, 0),
    }),
  },
  [TileType.KEY]: {
    type: TileType.KEY,
    name: 'Key Tile',
    description: `Grants ${GAME_CONFIG.KEY_REWARD} keys`,
    apply: (player: Player) => ({
      ...player,
      keys: player.keys + GAME_CONFIG.KEY_REWARD,
    }),
  },
  [TileType.TREASURE_CHEST]: {
    type: TileType.TREASURE_CHEST,
    name: 'Treasure Chest',
    description: `Requires ${GAME_CONFIG.KEYS_TO_WIN} keys to open and win`,
    apply: (player: Player) => player,
  },
};

// Calculate sequential looped position for tiles with selective spacing adjustments
export const calculateSequentialPosition = (index: number, totalTiles: number) => {
  // Create a single continuous path that forms a closed loop
  // This creates an oval/racetrack layout perfect for board games
  // Selective outward positioning for specific tile ranges to improve spacing

  const progress = index / totalTiles; // 0 to 1
  const angle = progress * 2 * Math.PI; // Full circle

  // Create an oval shape by varying the radius - further increased for better spacing
  const baseRadius = 32; // Further increased from 26 to 32 for more spacing
  const radiusVariation = 12; // Increased from 10 to 12 for larger oval

  // Create oval by modulating radius with cos(2*angle)
  const radius = baseRadius + radiusVariation * Math.cos(2 * angle);

  // Apply selective outward adjustments for specific tile ranges
  let finalRadius = radius;

  // Move tiles 12-20 outward by half a tile diameter (1.5 units) for better spacing
  if (index >= 11 && index <= 19) { // index is 0-based, so tiles 12-20 are index 11-19
    finalRadius = radius + 1.5; // Move outward by 1.5 units (half tile diameter)
  }

  // Move tiles 42-50 outward by half a tile diameter (1.5 units) for better spacing
  if (index >= 41 && index <= 49) { // index is 0-based, so tiles 42-50 are index 41-49
    finalRadius = radius + 1.5; // Move outward by 1.5 units (half tile diameter)
  }

  // Add slight vertical variation for more interesting layout
  const heightVariation = Math.sin(4 * angle) * 0.5;

  return {
    x: Math.cos(angle) * finalRadius,
    y: heightVariation,
    z: Math.sin(angle) * finalRadius,
  };
};

export const initPlayers = (ctx: Ctx): Record<string, Player> => {
  const players: Record<string, Player> = {} as Record<string, Player>;
  for (let i = 0; i < ctx.numPlayers; i++) {
    players[i] = {
      ID: i.toString(),
      name: `Player ${i + 1}`,
      health: GAME_CONFIG.STARTING_HEALTH,
      keys: GAME_CONFIG.STARTING_KEYS,
      position: GAME_CONFIG.STARTING_POSITION,
      color: GAME_CONFIG.PLAYER_COLORS[i % GAME_CONFIG.PLAYER_COLORS.length],
      isAlive: true,
    };
  }
  return players;
};

export const initBoard = (boardSize: number = GAME_CONFIG.DEFAULT_BOARD_SIZE): Tile[] => {
  const tiles: Tile[] = [];

  for (let i = 1; i <= boardSize; i++) {
    let tileType: TileType = TileType.BASIC;

    // Set treasure chest at specified position
    if (i === GAME_CONFIG.TREASURE_CHEST_POSITION) {
      tileType = TileType.TREASURE_CHEST;
    } else {
      // Randomly distribute other tile types
      const rand = Math.random();
      if (rand < 0.15) tileType = TileType.HEALING;
      else if (rand < 0.25) tileType = TileType.DAMAGE;
      else if (rand < 0.35) tileType = TileType.KEY;
      // else remains BASIC
    }

    // Calculate 3D position using sequential loop layout
    const position = calculateSequentialPosition(i - 1, boardSize);

    tiles.push({
      id: i,
      type: tileType,
      position,
    });
  }

  return tiles;
};

// Helper function to apply tile effects
export const applyTileEffect = (G: GameState, playerId: string): void => {
  const player = G.players[playerId];
  const tile = G.tiles.find(t => t.id === player.position);

  if (!tile) return;

  const effect = TILE_EFFECTS[tile.type];
  const updatedPlayer = effect.apply(player);

  // Update player state
  G.players[playerId] = updatedPlayer;

  // Add to game log
  G.gameLog.push({
    playerId,
    action: 'tile_effect',
    details: `${player.name} landed on ${effect.name}: ${effect.description}`,
    timestamp: Date.now(),
  });

  // Check if player died
  if (updatedPlayer.health <= 0) {
    G.players[playerId].isAlive = false;
    G.players[playerId].health = GAME_CONFIG.STARTING_HEALTH;
    G.players[playerId].position = GAME_CONFIG.STARTING_POSITION;

    G.gameLog.push({
      playerId,
      action: 'respawn',
      details: `${player.name} died and respawned at the starting position`,
      timestamp: Date.now(),
    });
  }
};

export const BrawlPartyGame: Game<GameState> = {
  name: GAME_ID,
  minPlayers: 2,
  maxPlayers: 6,

  setup: ({ ctx }) => ({
    players: initPlayers(ctx),
    tiles: initBoard(),
    currentDiceRoll: 0,
    gameLog: [],
    gameConfig: {
      boardSize: GAME_CONFIG.DEFAULT_BOARD_SIZE,
      treasureChestPosition: GAME_CONFIG.TREASURE_CHEST_POSITION,
      keysToWin: GAME_CONFIG.KEYS_TO_WIN,
    },
  }),

  moves: {
    rollDice,
    movePlayer,
    openTreasureChest,
  },

  turn: {
    onBegin: ({ G, ctx, events }) => {
      const currentPlayer = G.players[ctx.currentPlayer];

      // Check if current player is alive
      if (!currentPlayer.isAlive) {
        events.pass();
        return;
      }

      // Add turn start to log
      G.gameLog.push({
        playerId: ctx.currentPlayer,
        action: 'turn_start',
        details: `${currentPlayer.name}'s turn begins`,
        timestamp: Date.now(),
      });
    },

    onEnd: ({ G, ctx, events }) => {
      // Check win conditions
      const currentPlayer = G.players[ctx.currentPlayer];

      // Check if player won by opening treasure chest
      if (currentPlayer.position === G.gameConfig.treasureChestPosition &&
          currentPlayer.keys >= G.gameConfig.keysToWin) {
        events.endGame({
          winner: ctx.currentPlayer,
        });
        return;
      }

      // Check if only one player is alive
      const alivePlayers = Object.values(G.players).filter(p => p.isAlive);
      if (alivePlayers.length === 1) {
        events.endGame({
          winner: alivePlayers[0].ID,
        });
      }
    },
  },

  endIf: ({ G }) => {
    // Game ends if someone wins or only one player is alive
    const alivePlayers = Object.values(G.players).filter(p => p.isAlive);
    return alivePlayers.length <= 1;
  },
};

// Export with old name for compatibility
export const SantoriniGame = BrawlPartyGame;
