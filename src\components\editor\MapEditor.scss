.map-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: #ffffff;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
  }

  &__title {
    display: flex;
    align-items: center;
    gap: 8px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #2c3e50;
    }

    .dirty-indicator {
      color: #e74c3c;
      font-size: 20px;
      font-weight: bold;
    }
  }

  &__actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  &__content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }

  &__main {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  &__viewport {
    position: relative;
    flex: 1;
    background: #000;
    overflow: hidden;
  }

  &__viewport-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;

    .loading-overlay,
    .error-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 20px;
      border-radius: 8px;
      pointer-events: auto;
    }

    .error-overlay {
      background: rgba(220, 53, 69, 0.9);
    }

    .preview-overlay {
      position: absolute;
      top: 20px;
      right: 20px;

      .preview-badge {
        background: rgba(40, 167, 69, 0.9);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
      }
    }
  }

  &__sidebar {
    width: 320px;
    background: #ffffff;
    border-left: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;

    &.collapsed {
      width: 40px;

      .sidebar-content {
        display: none;
      }
    }

    .sidebar-header {
      padding: 12px;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: flex-end;

      .sidebar-toggle {
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background: #f8f9fa;
        }
      }
    }

    .sidebar-content {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
    }

    .sidebar-section {
      padding: 16px;
      border-bottom: 1px solid #f1f3f4;

      &:last-child {
        border-bottom: none;
        flex: 1;
      }

      h3 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
      }
    }
  }

  &__status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    font-size: 12px;
    color: #6c757d;

    .status-left,
    .status-right {
      display: flex;
      gap: 16px;
    }
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: #f8f9fa;
  }
}

// Button styles
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &--primary {
    background: #007bff;
    color: white;

    &:hover:not(:disabled) {
      background: #0056b3;
    }
  }

  &--secondary {
    background: #6c757d;
    color: white;

    &:hover:not(:disabled) {
      background: #545b62;
    }
  }

  &--success {
    background: #28a745;
    color: white;

    &:hover:not(:disabled) {
      background: #1e7e34;
    }
  }

  &--danger {
    background: #dc3545;
    color: white;

    &:hover:not(:disabled) {
      background: #c82333;
    }
  }

  &--outline {
    background: transparent;
    border: 1px solid #ced4da;
    color: #495057;

    &:hover:not(:disabled) {
      background: #f8f9fa;
    }
  }
}

// Loading spinner
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  padding: 24px;
  min-width: 400px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

  h2 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 20px;
    font-weight: 600;
  }

  .form-group {
    margin-bottom: 16px;

    label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #495057;
    }

    input,
    select,
    textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.2s ease;

      &:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }
    }

    textarea {
      resize: vertical;
      min-height: 80px;
    }
  }

  .form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .map-editor {
    &__sidebar {
      width: 280px;

      &.collapsed {
        width: 40px;
      }
    }
  }
}

@media (max-width: 768px) {
  .map-editor {
    &__header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    &__title {
      justify-content: center;
    }

    &__actions {
      justify-content: center;
    }

    &__sidebar {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      z-index: 200;
      box-shadow: -4px 0 8px rgba(0, 0, 0, 0.1);

      &.collapsed {
        width: 40px;
      }
    }

    &__status {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;

      .status-left,
      .status-right {
        justify-content: center;
      }
    }
  }

  .modal {
    min-width: 300px;
    margin: 20px;
  }
}
